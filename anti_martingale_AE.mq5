//+------------------------------------------------------------------+
//|                                           Enhanced_Martingale_EA |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link "https://www.mql5.com"
#property version "2.00"
#property description "Advanced M1 Scalping EA with Multi-Indicator Analysis"

#include <Trade\Trade.mqh>

//--- Input Parameters
input group "=== TRADING SETTINGS ==="
input double InitialLot = 0.05;                    // Initial lot size
input double TakeProfit = 0.005;                   // Take profit percentage (0.5%)
input double MaxDailyLoss = 0.15;                  // Maximum daily loss (15%)
input int MaxSimultaneousOrders = 3;               // Maximum simultaneous orders
input bool UseFibonacciProgression = true;         // Use Fibonacci lot progression
input bool EnableContinuousTrading = true;         // Always return BUY/SELL signals

input group "=== TECHNICAL ANALYSIS ==="
input int MA_Fast_Period = 20;                     // Fast MA period
input int MA_Slow_Period = 50;                     // Slow MA period
input int RSI_Period = 14;                         // RSI period
input int ADX_Period = 14;                         // ADX period
input int MACD_Fast = 12;                          // MACD fast EMA
input int MACD_Slow = 26;                          // MACD slow EMA
input int MACD_Signal = 9;                         // MACD signal line
input int Stoch_K = 5;                             // Stochastic %K
input int Stoch_D = 3;                             // Stochastic %D
input int Stoch_Slowing = 3;                       // Stochastic slowing

input group "=== RISK MANAGEMENT ==="
input double AccountBalance = 0;                // Account balance (0 = auto)
input double DailyProfitTarget = 1000;              // Daily profit target
input bool UseTrailingStop = false;                // Use trailing stop (DISABLED for Anti-Martingale)
input double TrailingStopPips = 10;                // Trailing stop in pips
input bool UseAntiMartingalePrecision = true;      // Use precise SL/TP targets (recommended)

//--- Global Variables
CTrade Trade;

//--- Indicator Handles
int g_maFastHandle = INVALID_HANDLE;
int g_maSlowHandle = INVALID_HANDLE;
int g_rsiHandle = INVALID_HANDLE;
int g_adxHandle = INVALID_HANDLE;
int g_macdHandle = INVALID_HANDLE;
int g_stochHandle = INVALID_HANDLE;
int g_bbHandle = INVALID_HANDLE;

//--- Trading Variables
double g_accountBalance = 0;
double g_dailyStartBalance = 0;
double g_dailyProfit = 0;
double g_targetProfit = 0;
datetime g_lastTradeTime = 0;
datetime g_dailyStartTime = 0;
datetime g_lastLogTime = 0;
int g_activeOrders = 0;
bool g_tradingEnabled = true;

//--- Enhanced Anti-Martingale progression system
double g_winSequence[] = {1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0}; // Progressive win multipliers
double g_profitSteps[] = {1, 2, 3, 5, 8, 13, 21, 34, 55}; // Fibonacci-like profit progression
double g_lossSteps[] = {1, 1, 2, 3, 5, 8, 13, 21, 34}; // Loss progression (previous step)
int g_consecutiveWins = 0;
int g_consecutiveLosses = 0;
double g_currentWinStreak = 0.0;
double g_maxWinStreak = 0.0;
bool g_isInWinningStreak = false;
int g_currentStep = 0; // Current Anti-Martingale step (0-based)

//--- Signal weights for multi-indicator analysis
struct SignalWeights
{
    double ma_weight;
    double rsi_weight;
    double adx_weight;
    double macd_weight;
    double stoch_weight;
    double bb_weight;
};

SignalWeights g_weights = {0.25, 0.20, 0.20, 0.15, 0.10, 0.10};

//--- Enhanced order tracking structure
struct OrderInfo
{
    ulong ticket;
    int type;
    double lots;
    double openPrice;
    double targetProfit;
    double stopLoss;
    datetime openTime;
    string comment;
    double signalStrength;      // Signal confidence (0.0 to 1.0)
    bool isWinningTrade;        // Track if trade is currently profitable
    double maxProfit;           // Maximum profit achieved
    double maxLoss;             // Maximum loss experienced
};

//--- Trade outcome tracking
struct TradeOutcome
{
    bool isWin;
    double profit;
    double signalStrength;
    datetime closeTime;
    int consecutiveWins;
    int consecutiveLosses;
};

OrderInfo g_orders[];
TradeOutcome g_recentTrades[10]; // Track last 10 trades for analysis
int g_orderCount = 0;
int g_tradeHistoryIndex = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize account balance
    g_accountBalance = (AccountBalance == 0) ? AccountInfoDouble(ACCOUNT_BALANCE) : AccountBalance;
    g_dailyStartBalance = g_accountBalance;
    g_targetProfit = g_accountBalance * TakeProfit;
    g_dailyStartTime = TimeCurrent();

    // Initialize indicator handles
    if(!InitializeIndicators())
    {
        LogMessage("ERROR: Failed to initialize indicators");
        return(INIT_FAILED);
    }

    // Configure trade settings
    Trade.SetDeviationInPoints(30);  // Increased for volatile markets
    Trade.SetTypeFilling(ORDER_FILLING_IOC);  // Better for scalping
    Trade.SetAsyncMode(false);

    // Initialize order tracking and Anti-Martingale system
    ArrayResize(g_orders, 0);
    g_orderCount = 0;
    g_consecutiveWins = 0;
    g_consecutiveLosses = 0;
    g_currentWinStreak = 0.0;
    g_maxWinStreak = 0.0;
    g_isInWinningStreak = false;
    g_currentStep = 0; // Start at step 1 (index 0)
    g_tradeHistoryIndex = 0;

    // Initialize trade history
    for(int i = 0; i < 10; i++)
    {
        g_recentTrades[i].isWin = false;
        g_recentTrades[i].profit = 0.0;
        g_recentTrades[i].signalStrength = 0.0;
        g_recentTrades[i].closeTime = 0;
        g_recentTrades[i].consecutiveWins = 0;
        g_recentTrades[i].consecutiveLosses = 0;
    }

    // Log initialization
    LogMessage("=== ENHANCED MARTINGALE EA INITIALIZED ===");
    LogMessage(StringFormat("Account Balance: %.2f", g_accountBalance));
    LogMessage(StringFormat("Daily Profit Target: %.2f", DailyProfitTarget));
    LogMessage(StringFormat("Max Daily Loss: %.1f%%", MaxDailyLoss * 100));
    LogMessage(StringFormat("Initial Lot Size: %.2f", InitialLot));
    LogMessage(StringFormat("Max Simultaneous Orders: %d", MaxSimultaneousOrders));
    LogMessage(StringFormat("Fibonacci Progression: %s", UseFibonacciProgression ? "Enabled" : "Disabled"));
    LogMessage("=== INITIALIZATION COMPLETE ===");

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    static bool isInitialized = false;
    static int tickCount = 0;
    static datetime lastTradeCheck = 0;

    tickCount++;

    // Allow some ticks for indicators to initialize
    if(!isInitialized && tickCount < 10)
    {
        LogMessage(StringFormat("Initializing indicators... Tick: %d", tickCount));
        return;
    }

    if(!isInitialized)
    {
        isInitialized = true;
        LogMessage("=== EA READY FOR TRADING ===");
        LogMessage("Indicators initialized successfully");
    }

    // Update daily profit tracking
    UpdateDailyProfit();

    // Check daily loss limit (15% protection)
    if(CheckDailyLossLimit())
    {
        LogMessage("DAILY LOSS LIMIT REACHED - Trading disabled for today");
        g_tradingEnabled = false;
        CloseAllPositions();
        return;
    }

    // Check daily profit target
    if(g_dailyProfit >= DailyProfitTarget)
    {
        LogMessage(StringFormat("DAILY PROFIT TARGET ACHIEVED: %.2f", g_dailyProfit));
        g_tradingEnabled = false;
        CloseAllPositions();
        return;
    }

    // Skip trading if disabled
    if(!g_tradingEnabled)
        return;

    // Update order tracking every tick
    UpdateOrderTracking();

    // Monitor existing positions every tick for best profit optimization
    MonitorPositions();

    // Check for new trades (M1 scalping - strict 60-second intervals)
    datetime currentTime = TimeCurrent();
    bool shouldCheckTrade = false;

    // Only allow new trades every 60 seconds minimum
    if(currentTime - lastTradeCheck >= 60)
    {
        shouldCheckTrade = true;
        LogMessage(StringFormat("M1 Interval Check: %d seconds since last trade check",
                  (int)(currentTime - lastTradeCheck)));
    }

    // Exception: Allow immediate entry only if no orders AND more than 60 seconds since EA start
    if(g_activeOrders == 0 && currentTime - g_lastTradeTime >= 60)
    {
        shouldCheckTrade = true;
        LogMessage("No active orders - immediate entry allowed (60s+ since last trade)");
    }

    if(shouldCheckTrade && g_activeOrders < MaxSimultaneousOrders)
    {
        lastTradeCheck = currentTime;

        // Get market signal with strength (always BUY or SELL, never NONE)
        double signalStrength = 0.0;
        int signal = GetMarketSignal(signalStrength);

        LogMessage(StringFormat("Market signal: %s (Strength: %.2f)",
                  (signal == ORDER_TYPE_BUY) ? "BUY" : "SELL", signalStrength));

        // Execute trading logic with signal strength
        ExecuteTradeSignal(signal, signalStrength);
    }

    // Log status every 5 minutes
    if(currentTime - g_lastLogTime >= 300)
    {
        LogTradingStatus();
        g_lastLogTime = currentTime;
    }
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release all indicator handles
    if(g_maFastHandle != INVALID_HANDLE) IndicatorRelease(g_maFastHandle);
    if(g_maSlowHandle != INVALID_HANDLE) IndicatorRelease(g_maSlowHandle);
    if(g_rsiHandle != INVALID_HANDLE) IndicatorRelease(g_rsiHandle);
    if(g_adxHandle != INVALID_HANDLE) IndicatorRelease(g_adxHandle);
    if(g_macdHandle != INVALID_HANDLE) IndicatorRelease(g_macdHandle);
    if(g_stochHandle != INVALID_HANDLE) IndicatorRelease(g_stochHandle);
    if(g_bbHandle != INVALID_HANDLE) IndicatorRelease(g_bbHandle);

    LogMessage("=== EA DEINITIALIZED ===");
    LogMessage(StringFormat("Final Daily P&L: %.2f", g_dailyProfit));
    LogMessage(StringFormat("Total Orders Executed: %d", g_orderCount));
}

//+------------------------------------------------------------------+
//| Initialize all technical indicators                              |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
    // Moving Averages
    g_maFastHandle = iMA(Symbol(), PERIOD_M1, MA_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
    g_maSlowHandle = iMA(Symbol(), PERIOD_M1, MA_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);

    // RSI
    g_rsiHandle = iRSI(Symbol(), PERIOD_M1, RSI_Period, PRICE_CLOSE);

    // ADX
    g_adxHandle = iADX(Symbol(), PERIOD_M1, ADX_Period);

    // MACD
    g_macdHandle = iMACD(Symbol(), PERIOD_M1, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);

    // Stochastic
    g_stochHandle = iStochastic(Symbol(), PERIOD_M1, Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);

    // Bollinger Bands
    g_bbHandle = iBands(Symbol(), PERIOD_M1, 20, 0, 2.0, PRICE_CLOSE);

    // Verify all handles are valid
    if(g_maFastHandle == INVALID_HANDLE || g_maSlowHandle == INVALID_HANDLE ||
       g_rsiHandle == INVALID_HANDLE || g_adxHandle == INVALID_HANDLE ||
       g_macdHandle == INVALID_HANDLE || g_stochHandle == INVALID_HANDLE ||
       g_bbHandle == INVALID_HANDLE)
    {
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Get enhanced market signal using multiple indicators             |
//+------------------------------------------------------------------+
int GetMarketSignal(double &signalStrength)
{
    double totalScore = 0.0;
    int validSignals = 0;
    signalStrength = 0.0;

    // Moving Average Signal
    double maSignal = GetMASignal();
    if(maSignal != 0.0)
    {
        totalScore += maSignal * g_weights.ma_weight;
        validSignals++;
    }

    // RSI Signal
    double rsiSignal = GetRSISignal();
    if(rsiSignal != 0.0)
    {
        totalScore += rsiSignal * g_weights.rsi_weight;
        validSignals++;
    }

    // ADX Signal
    double adxSignal = GetADXSignal();
    if(adxSignal != 0.0)
    {
        totalScore += adxSignal * g_weights.adx_weight;
        validSignals++;
    }

    // MACD Signal
    double macdSignal = GetMACDSignal();
    if(macdSignal != 0.0)
    {
        totalScore += macdSignal * g_weights.macd_weight;
        validSignals++;
    }

    // Stochastic Signal
    double stochSignal = GetStochasticSignal();
    if(stochSignal != 0.0)
    {
        totalScore += stochSignal * g_weights.stoch_weight;
        validSignals++;
    }

    // Bollinger Bands Signal
    double bbSignal = GetBollingerSignal();
    if(bbSignal != 0.0)
    {
        totalScore += bbSignal * g_weights.bb_weight;
        validSignals++;
    }

    // Calculate signal strength (0.0 to 1.0)
    if(validSignals > 0)
    {
        signalStrength = MathMin(MathAbs(totalScore), 1.0);
    }
    else
    {
        signalStrength = 0.1; // Low confidence for price action fallback
    }

    // Debug logging
    LogMessage(StringFormat("Signal Analysis: MA=%.2f, RSI=%.2f, ADX=%.2f, MACD=%.2f, Stoch=%.2f, BB=%.2f",
              maSignal, rsiSignal, adxSignal, macdSignal, stochSignal, bbSignal));
    LogMessage(StringFormat("Total Score: %.3f, Valid Signals: %d, Strength: %.2f", totalScore, validSignals, signalStrength));

    // If no valid signals, use simple price action
    if(validSignals == 0)
    {
        LogMessage("No valid indicator signals - using price action");
        double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
        double prevPrice = iClose(Symbol(), PERIOD_M1, 1);
        return (currentPrice > prevPrice) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    }

    // Ensure continuous trading - never return neutral
    if(EnableContinuousTrading)
    {
        return (totalScore >= 0) ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
    }

    // Standard signal interpretation
    if(totalScore > 0.3) return ORDER_TYPE_BUY;
    if(totalScore < -0.3) return ORDER_TYPE_SELL;

    // Default to BUY if neutral (continuous trading requirement)
    return ORDER_TYPE_BUY;
}

//+------------------------------------------------------------------+
//| Individual indicator signal functions                            |
//+------------------------------------------------------------------+
double GetMASignal()
{
    if(g_maFastHandle == INVALID_HANDLE || g_maSlowHandle == INVALID_HANDLE)
        return 0.0;

    double maFast[], maSlow[];
    ArraySetAsSeries(maFast, true);
    ArraySetAsSeries(maSlow, true);

    int fastCopied = CopyBuffer(g_maFastHandle, 0, 0, 2, maFast);
    int slowCopied = CopyBuffer(g_maSlowHandle, 0, 0, 2, maSlow);

    if(fastCopied < 2 || slowCopied < 2)
    {
        LogMessage(StringFormat("MA Signal Error: Fast copied %d, Slow copied %d", fastCopied, slowCopied));
        return 0.0;
    }

    // Current and previous values
    double fastCurrent = maFast[0], fastPrev = maFast[1];
    double slowCurrent = maSlow[0], slowPrev = maSlow[1];

    // Validate data
    if(fastCurrent <= 0 || slowCurrent <= 0 || fastPrev <= 0 || slowPrev <= 0)
        return 0.0;

    // Signal strength based on crossover and distance
    if(fastCurrent > slowCurrent)
    {
        double strength = MathMin((fastCurrent - slowCurrent) / slowCurrent * 100, 1.0);
        return (fastCurrent > fastPrev) ? strength : strength * 0.5;
    }
    else
    {
        double strength = MathMin((slowCurrent - fastCurrent) / slowCurrent * 100, 1.0);
        return (fastCurrent < fastPrev) ? -strength : -strength * 0.5;
    }
}

double GetRSISignal()
{
    if(g_rsiHandle == INVALID_HANDLE)
        return 0.0;

    double rsi[];
    ArraySetAsSeries(rsi, true);

    int copied = CopyBuffer(g_rsiHandle, 0, 0, 1, rsi);
    if(copied < 1)
    {
        LogMessage(StringFormat("RSI Signal Error: Copied %d values", copied));
        return 0.0;
    }

    double rsiValue = rsi[0];

    // Validate RSI value
    if(rsiValue < 0 || rsiValue > 100)
        return 0.0;

    // RSI signal interpretation
    if(rsiValue > 70) return -0.8;      // Overbought - sell signal
    if(rsiValue < 30) return 0.8;       // Oversold - buy signal
    if(rsiValue > 50) return 0.3;       // Above midline - weak buy
    return -0.3;                        // Below midline - weak sell
}

double GetADXSignal()
{
    double adx[], plusDI[], minusDI[];
    ArraySetAsSeries(adx, true);
    ArraySetAsSeries(plusDI, true);
    ArraySetAsSeries(minusDI, true);

    if(CopyBuffer(g_adxHandle, 0, 0, 1, adx) < 1 ||
       CopyBuffer(g_adxHandle, 1, 0, 1, plusDI) < 1 ||
       CopyBuffer(g_adxHandle, 2, 0, 1, minusDI) < 1)
        return 0.0;

    double adxValue = adx[0];
    double plusValue = plusDI[0];
    double minusValue = minusDI[0];

    // Strong trend confirmation
    if(adxValue > 25)
    {
        double strength = MathMin(adxValue / 50.0, 1.0);
        return (plusValue > minusValue) ? strength : -strength;
    }

    return 0.0; // Weak trend
}

double GetMACDSignal()
{
    double macd[], signal[];
    ArraySetAsSeries(macd, true);
    ArraySetAsSeries(signal, true);

    if(CopyBuffer(g_macdHandle, 0, 0, 2, macd) < 2 ||
       CopyBuffer(g_macdHandle, 1, 0, 2, signal) < 2)
        return 0.0;

    double macdCurrent = macd[0], macdPrev = macd[1];
    double signalCurrent = signal[0], signalPrev = signal[1];

    // MACD crossover signals
    if(macdCurrent > signalCurrent && macdPrev <= signalPrev)
        return 0.7; // Bullish crossover
    if(macdCurrent < signalCurrent && macdPrev >= signalPrev)
        return -0.7; // Bearish crossover

    // MACD position relative to signal line
    return (macdCurrent > signalCurrent) ? 0.3 : -0.3;
}

double GetStochasticSignal()
{
    double stochMain[], stochSignal[];
    ArraySetAsSeries(stochMain, true);
    ArraySetAsSeries(stochSignal, true);

    if(CopyBuffer(g_stochHandle, 0, 0, 1, stochMain) < 1 ||
       CopyBuffer(g_stochHandle, 1, 0, 1, stochSignal) < 1)
        return 0.0;

    double mainValue = stochMain[0];
    double signalValue = stochSignal[0];

    // Stochastic signal interpretation
    if(mainValue > 80) return -0.6;     // Overbought
    if(mainValue < 20) return 0.6;      // Oversold
    return (mainValue > signalValue) ? 0.2 : -0.2;
}

double GetBollingerSignal()
{
    double bbUpper[], bbMiddle[], bbLower[];
    ArraySetAsSeries(bbUpper, true);
    ArraySetAsSeries(bbMiddle, true);
    ArraySetAsSeries(bbLower, true);

    if(CopyBuffer(g_bbHandle, 1, 0, 1, bbUpper) < 1 ||
       CopyBuffer(g_bbHandle, 0, 0, 1, bbMiddle) < 1 ||
       CopyBuffer(g_bbHandle, 2, 0, 1, bbLower) < 1)
        return 0.0;

    double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double upperBand = bbUpper[0];
    double lowerBand = bbLower[0];
    double middleBand = bbMiddle[0];

    // Bollinger Bands signal
    if(currentPrice > upperBand) return -0.5;  // Price above upper band - sell
    if(currentPrice < lowerBand) return 0.5;   // Price below lower band - buy
    return (currentPrice > middleBand) ? 0.1 : -0.1;
}

//+------------------------------------------------------------------+
//| Execute trade signal with enhanced risk management               |
//+------------------------------------------------------------------+
void ExecuteTradeSignal(int signal, double signalStrength)
{
    // Validate trading conditions
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
    {
        LogMessage("ERROR: Trading not allowed in terminal");
        return;
    }

    if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
    {
        LogMessage("ERROR: EA trading not allowed");
        return;
    }

    double lotSize = CalculateLotSize(signalStrength);
    if(lotSize <= 0)
    {
        LogMessage("ERROR: Invalid lot size calculated");
        return;
    }

    double price = (signal == ORDER_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl = 0, tp = 0;

    // Calculate stop loss and take profit based on profit/loss targets
    CalculateStopLevels(signal, price, sl, tp, lotSize);

    string comment = StringFormat("EA_Order_%d", g_orderCount + 1);
    ulong ticket = 0;

    LogMessage(StringFormat("Attempting to open %s order: %.2f lots at %.5f",
              (signal == ORDER_TYPE_BUY) ? "BUY" : "SELL", lotSize, price));

    // Execute trade
    if(signal == ORDER_TYPE_BUY)
    {
        if(Trade.Buy(lotSize, Symbol(), price, sl, tp, comment))
            ticket = Trade.ResultOrder();
    }
    else
    {
        if(Trade.Sell(lotSize, Symbol(), price, sl, tp, comment))
            ticket = Trade.ResultOrder();
    }

    // Track the order
    if(ticket > 0)
    {
        AddOrderToTracking(ticket, signal, lotSize, price, tp, sl, comment, signalStrength);
        LogMessage(StringFormat("✅ ORDER OPENED: %s %.2f lots at %.3f [Ticket: %d]",
                  (signal == ORDER_TYPE_BUY) ? "BUY" : "SELL", lotSize, price, ticket));
        LogMessage(StringFormat("📊 INITIAL SL/TP: SL=%.3f, TP=%.3f (Signal: %.2f)",
                  sl, tp, signalStrength));
        LogMessage(StringFormat("🔒 ANTI-MARTINGALE PRECISION: %s (Trailing Stop: %s)",
                  UseAntiMartingalePrecision ? "ENABLED" : "DISABLED",
                  UseTrailingStop ? "ENABLED" : "DISABLED"));
        g_lastTradeTime = TimeCurrent();
    }
    else
    {
        int error = GetLastError();
        LogMessage(StringFormat("❌ ORDER FAILED: %s %.2f lots - Error: %d (%s)",
                  (signal == ORDER_TYPE_BUY) ? "BUY" : "SELL", lotSize, error, ErrorDescription(error)));

        // Reset error
        ResetLastError();
    }
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size using Enhanced Anti-Martingale       |
//+------------------------------------------------------------------+
double CalculateLotSize(double signalStrength)
{
    double baseLot = InitialLot;
    double finalLot = baseLot;

    // === ANTI-MARTINGALE CORE LOGIC ===
    // Increase lot size based on consecutive wins, reset on losses

    LogMessage(StringFormat("Anti-Martingale Debug: ConsecWins=%d, InStreak=%s, WinStreak=%.2f",
              g_consecutiveWins, g_isInWinningStreak ? "YES" : "NO", g_currentWinStreak));

    if(g_consecutiveWins > 0 && g_isInWinningStreak)
    {
        // Progressive scaling based on win streak
        int winIndex = MathMin(g_consecutiveWins - 1, ArraySize(g_winSequence) - 1);
        double winMultiplier = g_winSequence[winIndex];

        LogMessage(StringFormat("Anti-Martingale ACTIVE: %d consecutive wins, index=%d, multiplier=%.1f",
                  g_consecutiveWins, winIndex, winMultiplier));

        finalLot = baseLot * winMultiplier;
    }
    else
    {
        // Reset to base lot after any loss (Anti-Martingale principle)
        finalLot = baseLot;
        LogMessage(StringFormat("Anti-Martingale RESET: Using base lot size (ConsecWins=%d, InStreak=%s)",
                  g_consecutiveWins, g_isInWinningStreak ? "YES" : "NO"));
    }

    // === SIGNAL STRENGTH ADJUSTMENT ===
    // Increase size for stronger signals, reduce for weaker ones
    double signalMultiplier = 0.5 + (signalStrength * 0.5); // Range: 0.5 to 1.0
    finalLot *= signalMultiplier;

    LogMessage(StringFormat("Signal adjustment: Strength %.2f, Multiplier %.2f",
              signalStrength, signalMultiplier));

    // === VOLATILITY ADJUSTMENT ===
    // Reduce lot size in high volatility periods
    double atr = CalculateATR();
    double avgATR = CalculateAverageATR();

    if(avgATR > 0)
    {
        double volatilityRatio = atr / avgATR;
        if(volatilityRatio > 1.5) // High volatility
        {
            finalLot *= 0.7; // Reduce by 30%
            LogMessage(StringFormat("High volatility detected (%.2f), reducing lot size", volatilityRatio));
        }
        else if(volatilityRatio < 0.7) // Low volatility
        {
            finalLot *= 1.2; // Increase by 20%
            LogMessage(StringFormat("Low volatility detected (%.2f), increasing lot size", volatilityRatio));
        }
    }

    // === RISK MANAGEMENT LIMITS ===
    // Maximum 5% of free margin per trade
    double maxLot = AccountInfoDouble(ACCOUNT_FREEMARGIN) * 0.05 / 1000;
    finalLot = MathMin(finalLot, maxLot);

    // Normalize lot size
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxAllowedLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    finalLot = MathMax(finalLot, minLot);
    finalLot = MathMin(finalLot, maxAllowedLot);
    finalLot = NormalizeDouble(finalLot / lotStep, 0) * lotStep;

    LogMessage(StringFormat("Final lot calculation: Base=%.2f, Final=%.2f", baseLot, finalLot));

    return finalLot;
}

//+------------------------------------------------------------------+
//| Calculate SL/TP based on profit/loss targets (Anti-Martingale)  |
//+------------------------------------------------------------------+
void CalculateStopLevels(int orderType, double price, double &sl, double &tp, double lotSize)
{
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS);

    // Get current account balance
    double accountBalance = (AccountBalance == 0) ? AccountInfoDouble(ACCOUNT_BALANCE) : AccountBalance;

    // Determine current Anti-Martingale step
    int stepIndex = MathMin(g_currentStep, ArraySize(g_profitSteps) - 1);

    // Calculate target profit and loss in account currency
    double targetProfitAmount = g_profitSteps[stepIndex] * TakeProfit * accountBalance;
    double targetLossAmount = g_lossSteps[stepIndex] * TakeProfit * accountBalance;

    LogMessage(StringFormat("Anti-Martingale Step %d: Target Profit=$%.2f, Target Loss=$%.2f",
              stepIndex + 1, targetProfitAmount, targetLossAmount));

    // === SIMPLIFIED CALCULATION - DIRECT APPROACH ===
    // For XAU/USD: Use direct calculation based on known values
    // 1 point = 0.001, 1 lot = 100 oz, 1 point movement = $0.10 per lot

    string symbolName = Symbol();
    LogMessage(StringFormat("Symbol: %s, Lot Size: %.2f", symbolName, lotSize));

    // DIRECT CALCULATION FOR XAU/USD
    // For 0.03 lots: 1 point = $0.003 (0.03 * $0.10)
    // To make $20: need 20 / 0.003 = 6667 points

    double pointValuePerLot = 0.10; // $0.10 per point per lot for XAU/USD (standard)
    double pointValueForThisLot = pointValuePerLot * lotSize;

    LogMessage(StringFormat("Point value per lot: $%.2f, For %.2f lots: $%.4f per point",
              pointValuePerLot, lotSize, pointValueForThisLot));

    // Calculate required point distances to achieve target amounts
    double profitPoints = targetProfitAmount / pointValueForThisLot;
    double lossPoints = targetLossAmount / pointValueForThisLot;

    LogMessage(StringFormat("Target: Profit=$%.2f, Loss=$%.2f", targetProfitAmount, targetLossAmount));
    LogMessage(StringFormat("Required distances: Profit=%.0f points, Loss=%.0f points",
              profitPoints, lossPoints));

    // Apply minimum distances for broker compliance
    double minStopLevel = SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * point;
    if(minStopLevel == 0) minStopLevel = 100 * point; // Default 100 points for XAU/USD

    double minPoints = minStopLevel / point;
    profitPoints = MathMax(profitPoints, minPoints * 2); // Minimum 2x broker requirement
    lossPoints = MathMax(lossPoints, minPoints * 2);

    LogMessage(StringFormat("Broker compliance: MinStopLevel=%.1f points, Final Profit=%.1f, Loss=%.1f",
              minPoints, profitPoints, lossPoints));

    // Calculate actual SL and TP prices
    double profitDistance = profitPoints * point;
    double lossDistance = lossPoints * point;

    if(orderType == ORDER_TYPE_BUY)
    {
        tp = price + profitDistance;
        sl = price - lossDistance;
    }
    else
    {
        tp = price - profitDistance;
        sl = price + lossDistance;
    }

    // Normalize prices
    sl = NormalizeDouble(sl, digits);
    tp = NormalizeDouble(tp, digits);

    // Final broker compliance check
    double currentPrice = (orderType == ORDER_TYPE_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK) :
                         SymbolInfoDouble(Symbol(), SYMBOL_BID);

    if(orderType == ORDER_TYPE_BUY)
    {
        if(sl >= currentPrice - minStopLevel)
        {
            sl = currentPrice - minStopLevel * 2;
            LogMessage(StringFormat("SL adjusted for broker compliance: %.3f", sl));
        }
        if(tp <= currentPrice + minStopLevel)
        {
            tp = currentPrice + minStopLevel * 2;
            LogMessage(StringFormat("TP adjusted for broker compliance: %.3f", tp));
        }
    }
    else
    {
        if(sl <= currentPrice + minStopLevel)
        {
            sl = currentPrice + minStopLevel * 2;
            LogMessage(StringFormat("SL adjusted for broker compliance: %.3f", sl));
        }
        if(tp >= currentPrice - minStopLevel)
        {
            tp = currentPrice - minStopLevel * 2;
            LogMessage(StringFormat("TP adjusted for broker compliance: %.3f", tp));
        }
    }

    // Calculate actual profit/loss amounts for verification
    double actualProfitPoints = MathAbs(tp - price) / point;
    double actualLossPoints = MathAbs(price - sl) / point;
    double actualProfitAmount = actualProfitPoints * pointValueForThisLot;
    double actualLossAmount = actualLossPoints * pointValueForThisLot;
    double riskRewardRatio = (actualLossPoints > 0) ? actualProfitPoints / actualLossPoints : 0;

    LogMessage(StringFormat("FINAL SL/TP: Entry=%.3f, SL=%.3f (%.0f pts), TP=%.3f (%.0f pts), R:R=1:%.2f",
              price, sl, actualLossPoints, tp, actualProfitPoints, riskRewardRatio));
    LogMessage(StringFormat("ACTUAL AMOUNTS: Profit=$%.2f, Loss=$%.2f (Target: $%.2f/$%.2f)",
              actualProfitAmount, actualLossAmount, targetProfitAmount, targetLossAmount));

    // Final validation check
    if(MathAbs(actualProfitAmount - targetProfitAmount) > 1.0)
    {
        LogMessage(StringFormat("WARNING: Profit amount mismatch! Actual=$%.2f, Target=$%.2f",
                  actualProfitAmount, targetProfitAmount));
    }
    if(MathAbs(actualLossAmount - targetLossAmount) > 1.0)
    {
        LogMessage(StringFormat("WARNING: Loss amount mismatch! Actual=$%.2f, Target=$%.2f",
                  actualLossAmount, targetLossAmount));
    }
}

//+------------------------------------------------------------------+
//| Calculate Average True Range for dynamic stops                  |
//+------------------------------------------------------------------+
double CalculateATR()
{
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    // Copy 15 bars to have enough data for ATR calculation
    if(CopyHigh(Symbol(), PERIOD_M1, 0, 15, high) < 15 ||
       CopyLow(Symbol(), PERIOD_M1, 0, 15, low) < 15 ||
       CopyClose(Symbol(), PERIOD_M1, 0, 15, close) < 15)
    {
        LogMessage("ATR Calculation: Insufficient data - using default value");
        return 0.001; // Default value
    }

    double atrSum = 0;
    // Start from index 0 and go to 13 (14 periods), using close[i+1] safely
    for(int i = 0; i < 14; i++)
    {
        // Additional safety check
        if(i + 1 >= ArraySize(close))
        {
            LogMessage(StringFormat("ATR Calculation: Array bounds check failed at index %d", i));
            break;
        }

        double tr1 = high[i] - low[i];
        double tr2 = MathAbs(high[i] - close[i+1]);
        double tr3 = MathAbs(low[i] - close[i+1]);
        atrSum += MathMax(tr1, MathMax(tr2, tr3));
    }

    return atrSum / 14;
}

//+------------------------------------------------------------------+
//| Calculate average ATR for volatility comparison                  |
//+------------------------------------------------------------------+
double CalculateAverageATR()
{
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    // Copy 51 bars to have enough data for 50-period ATR calculation
    if(CopyHigh(Symbol(), PERIOD_M1, 0, 51, high) < 51 ||
       CopyLow(Symbol(), PERIOD_M1, 0, 51, low) < 51 ||
       CopyClose(Symbol(), PERIOD_M1, 0, 51, close) < 51)
    {
        LogMessage("Average ATR Calculation: Insufficient data - using default value");
        return 0.001; // Default value
    }

    double atrSum = 0;
    // Start from index 0 and go to 49 (50 periods), using close[i+1] safely
    for(int i = 0; i < 50; i++)
    {
        // Additional safety check
        if(i + 1 >= ArraySize(close))
        {
            LogMessage(StringFormat("Average ATR Calculation: Array bounds check failed at index %d", i));
            break;
        }

        double tr1 = high[i] - low[i];
        double tr2 = MathAbs(high[i] - close[i+1]);
        double tr3 = MathAbs(low[i] - close[i+1]);
        atrSum += MathMax(tr1, MathMax(tr2, tr3));
    }

    return atrSum / 50;
}

//+------------------------------------------------------------------+
//| Update daily profit tracking                                     |
//+------------------------------------------------------------------+
void UpdateDailyProfit()
{
    // Check if new day started
    datetime currentTime = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(currentTime, dt);

    MqlDateTime startDt;
    TimeToStruct(g_dailyStartTime, startDt);

    if(dt.day != startDt.day)
    {
        // New day - reset counters
        g_dailyStartTime = currentTime;
        g_dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        g_dailyProfit = 0;

        // Reset Anti-Martingale counters for new day
        g_consecutiveWins = 0;
        g_consecutiveLosses = 0;
        g_currentWinStreak = 0.0;
        g_maxWinStreak = 0.0;
        g_isInWinningStreak = false;
        g_currentStep = 0; // Reset to step 1

        g_tradingEnabled = true;
        LogMessage("=== NEW TRADING DAY STARTED ===");
        LogMessage("Anti-Martingale counters reset for new day");
    }

    // Calculate current daily profit
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    g_dailyProfit = currentBalance - g_dailyStartBalance;
}

//+------------------------------------------------------------------+
//| Check daily loss limit (15% protection)                         |
//+------------------------------------------------------------------+
bool CheckDailyLossLimit()
{
    double lossPercentage = -g_dailyProfit / g_dailyStartBalance;
    return (lossPercentage >= MaxDailyLoss);
}

//+------------------------------------------------------------------+
//| Close all open positions                                         |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        ulong ticket = PositionGetTicket(i);
        if(PositionSelectByTicket(ticket))
        {
            if(PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                Trade.PositionClose(ticket);
                LogMessage(StringFormat("POSITION CLOSED: Ticket %d", ticket));
            }
        }
    }

    // Reset tracking
    ArrayResize(g_orders, 0);
    g_orderCount = 0;
    g_activeOrders = 0;
}

//+------------------------------------------------------------------+
//| Add order to tracking system                                     |
//+------------------------------------------------------------------+
void AddOrderToTracking(ulong ticket, int type, double lots, double openPrice, double tp, double sl, string comment, double signalStrength)
{
    int newSize = ArraySize(g_orders) + 1;
    ArrayResize(g_orders, newSize);

    g_orders[newSize-1].ticket = ticket;
    g_orders[newSize-1].type = type;
    g_orders[newSize-1].lots = lots;
    g_orders[newSize-1].openPrice = openPrice;
    g_orders[newSize-1].targetProfit = tp;
    g_orders[newSize-1].stopLoss = sl;
    g_orders[newSize-1].openTime = TimeCurrent();
    g_orders[newSize-1].comment = comment;
    g_orders[newSize-1].signalStrength = signalStrength;
    g_orders[newSize-1].isWinningTrade = false;
    g_orders[newSize-1].maxProfit = 0.0;
    g_orders[newSize-1].maxLoss = 0.0;

    g_orderCount++;
    g_activeOrders++;
}

//+------------------------------------------------------------------+
//| Update order tracking system                                     |
//+------------------------------------------------------------------+
void UpdateOrderTracking()
{
    g_activeOrders = 0;

    for(int i = ArraySize(g_orders) - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(g_orders[i].ticket))
        {
            g_activeOrders++;
        }
        else
        {
            // Position closed - remove from tracking
            for(int j = i; j < ArraySize(g_orders) - 1; j++)
            {
                g_orders[j] = g_orders[j + 1];
            }
            ArrayResize(g_orders, ArraySize(g_orders) - 1);
        }
    }
}

//+------------------------------------------------------------------+
//| Monitor existing positions for Anti-Martingale management       |
//+------------------------------------------------------------------+
void MonitorPositions()
{
    for(int i = 0; i < ArraySize(g_orders); i++)
    {
        if(PositionSelectByTicket(g_orders[i].ticket))
        {
            double currentProfit = PositionGetDouble(POSITION_PROFIT);
            double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
            int positionType = (int)PositionGetInteger(POSITION_TYPE);

            // Update trade tracking
            UpdateTradeTracking(i, currentProfit);

            // Check for trailing stop (conditional based on strategy)
            if(UseTrailingStop && !UseAntiMartingalePrecision)
            {
                ApplyTrailingStop(g_orders[i].ticket, positionType, openPrice);
            }
            else if(UseTrailingStop && UseAntiMartingalePrecision)
            {
                LogMessage(StringFormat("Trailing stop DISABLED for precise Anti-Martingale targets (Ticket: %d)",
                          g_orders[i].ticket));
            }

            // === ANTI-MARTINGALE PROFIT MANAGEMENT ===
            // Close profitable positions and prepare for larger next trade

            double profitTarget = g_targetProfit * g_orders[i].lots / InitialLot;

            if(currentProfit >= profitTarget)
            {
                // Close winning position
                if(Trade.PositionClose(g_orders[i].ticket))
                {
                    LogMessage(StringFormat("✅ WINNING TRADE CLOSED: Ticket %d, Profit: %.2f",
                              g_orders[i].ticket, currentProfit));

                    // Update Anti-Martingale counters
                    UpdateAntiMartingaleCounters(true, currentProfit, g_orders[i].signalStrength);

                    // Remove from tracking
                    RemoveOrderFromTracking(i);
                    return; // Exit to avoid array index issues
                }
            }

            // === LOSS MANAGEMENT ===
            // Close losing positions and reset to base lot

            double lossLimit = -profitTarget * 0.5; // 50% of profit target as loss limit

            if(currentProfit <= lossLimit)
            {
                // Close losing position
                if(Trade.PositionClose(g_orders[i].ticket))
                {
                    LogMessage(StringFormat("❌ LOSING TRADE CLOSED: Ticket %d, Loss: %.2f",
                              g_orders[i].ticket, currentProfit));

                    // Update Anti-Martingale counters
                    UpdateAntiMartingaleCounters(false, currentProfit, g_orders[i].signalStrength);

                    // Remove from tracking
                    RemoveOrderFromTracking(i);
                    return; // Exit to avoid array index issues
                }
            }

            // Log significant profit/loss changes
            if(MathAbs(currentProfit) > profitTarget * 0.5)
            {
                LogMessage(StringFormat("POSITION UPDATE: Ticket %d, P&L: %.2f (Target: %.2f)",
                          g_orders[i].ticket, currentProfit, profitTarget));
            }
        }
        else
        {
            // Position was closed externally - remove from tracking
            LogMessage(StringFormat("Position %d closed externally", g_orders[i].ticket));
            RemoveOrderFromTracking(i);
            return;
        }
    }
}

//+------------------------------------------------------------------+
//| Apply trailing stop to position with frequency control          |
//+------------------------------------------------------------------+
void ApplyTrailingStop(ulong ticket, int positionType, double openPrice)
{
    static datetime lastTrailTime = 0;
    datetime currentTime = TimeCurrent();

    // Limit trailing stop modifications to every 10 seconds minimum
    if(currentTime - lastTrailTime < 10)
        return;

    double currentPrice = (positionType == POSITION_TYPE_BUY) ?
                         SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                         SymbolInfoDouble(Symbol(), SYMBOL_ASK);

    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS);
    double pipValue = (digits == 5 || digits == 3) ? point * 10 : point;
    double trailDistance = TrailingStopPips * pipValue;

    // Get broker's minimum stop level
    double minStopLevel = SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL) * point;
    if(minStopLevel == 0) minStopLevel = 10 * point;

    // Ensure trail distance is at least 2x broker minimum
    trailDistance = MathMax(trailDistance, minStopLevel * 2);

    double currentSL = PositionGetDouble(POSITION_SL);
    double newSL = 0;
    bool shouldModify = false;

    if(positionType == POSITION_TYPE_BUY)
    {
        newSL = currentPrice - trailDistance;
        // Only modify if new SL is significantly better (at least 5 pips improvement)
        if(newSL > currentSL + (5 * pipValue) && newSL < currentPrice - minStopLevel)
        {
            shouldModify = true;
        }
    }
    else
    {
        newSL = currentPrice + trailDistance;
        // Only modify if new SL is significantly better (at least 5 pips improvement)
        if((currentSL == 0 || newSL < currentSL - (5 * pipValue)) && newSL > currentPrice + minStopLevel)
        {
            shouldModify = true;
        }
    }

    if(shouldModify)
    {
        newSL = NormalizeDouble(newSL, digits);
        if(Trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP)))
        {
            lastTrailTime = currentTime;
            LogMessage(StringFormat("Trailing Stop Updated: Ticket %d, New SL: %.5f", ticket, newSL));
        }
        else
        {
            LogMessage(StringFormat("Trailing Stop Failed: Ticket %d, Error: %d", ticket, GetLastError()));
        }
    }
}

//+------------------------------------------------------------------+
//| Update trade tracking for individual positions                   |
//+------------------------------------------------------------------+
void UpdateTradeTracking(int orderIndex, double currentProfit)
{
    // Update max profit/loss tracking
    if(currentProfit > g_orders[orderIndex].maxProfit)
        g_orders[orderIndex].maxProfit = currentProfit;

    if(currentProfit < g_orders[orderIndex].maxLoss)
        g_orders[orderIndex].maxLoss = currentProfit;

    // Update winning status
    g_orders[orderIndex].isWinningTrade = (currentProfit > 0);
}

//+------------------------------------------------------------------+
//| Update Anti-Martingale counters based on trade outcome          |
//+------------------------------------------------------------------+
void UpdateAntiMartingaleCounters(bool isWin, double profit, double signalStrength)
{
    // Record trade outcome in history
    g_recentTrades[g_tradeHistoryIndex].isWin = isWin;
    g_recentTrades[g_tradeHistoryIndex].profit = profit;
    g_recentTrades[g_tradeHistoryIndex].signalStrength = signalStrength;
    g_recentTrades[g_tradeHistoryIndex].closeTime = TimeCurrent();
    g_recentTrades[g_tradeHistoryIndex].consecutiveWins = g_consecutiveWins;
    g_recentTrades[g_tradeHistoryIndex].consecutiveLosses = g_consecutiveLosses;

    g_tradeHistoryIndex = (g_tradeHistoryIndex + 1) % 10; // Circular buffer

    if(isWin)
    {
        // === WINNING TRADE - ANTI-MARTINGALE LOGIC ===
        g_consecutiveWins++;
        g_consecutiveLosses = 0;
        g_currentWinStreak += profit;
        g_isInWinningStreak = true;

        // Advance to next Anti-Martingale step
        g_currentStep = MathMin(g_consecutiveWins - 1, ArraySize(g_profitSteps) - 1);

        if(g_currentWinStreak > g_maxWinStreak)
            g_maxWinStreak = g_currentWinStreak;

        LogMessage(StringFormat("🎯 WIN STREAK: %d consecutive wins, Step %d, Total profit: %.2f",
                  g_consecutiveWins, g_currentStep + 1, g_currentWinStreak));

        // Show next trade targets
        int nextStep = MathMin(g_currentStep + 1, ArraySize(g_profitSteps) - 1);
        double accountBalance = (AccountBalance == 0) ? AccountInfoDouble(ACCOUNT_BALANCE) : AccountBalance;
        double nextProfit = g_profitSteps[nextStep] * TakeProfit * accountBalance;
        double nextLoss = g_lossSteps[nextStep] * TakeProfit * accountBalance;

        LogMessage(StringFormat("📈 NEXT TRADE: Step %d, Target Profit: $%.2f, Target Loss: $%.2f",
                  nextStep + 1, nextProfit, nextLoss));

        // Profit protection: Take partial profits after 3+ wins
        if(g_consecutiveWins >= 3)
        {
            LogMessage("💰 PROFIT PROTECTION: Consider taking partial profits");
        }
    }
    else
    {
        // === LOSING TRADE - RESET TO BASE ===
        g_consecutiveLosses++;
        g_consecutiveWins = 0;
        g_currentWinStreak = 0.0;
        g_isInWinningStreak = false;
        g_currentStep = 0; // Reset to step 1

        LogMessage(StringFormat("📉 LOSS: Resetting to Step 1. Consecutive losses: %d",
                  g_consecutiveLosses));

        // Show reset targets
        double accountBalance = (AccountBalance == 0) ? AccountInfoDouble(ACCOUNT_BALANCE) : AccountBalance;
        double resetProfit = g_profitSteps[0] * TakeProfit * accountBalance;
        double resetLoss = g_lossSteps[0] * TakeProfit * accountBalance;

        LogMessage(StringFormat("🔄 RESET: Step 1, Target Profit: $%.2f, Target Loss: $%.2f",
                  resetProfit, resetLoss));

        // Additional risk management for consecutive losses
        if(g_consecutiveLosses >= 3)
        {
            LogMessage("⚠️ WARNING: 3+ consecutive losses - Consider reducing base lot size");
        }
    }

    // Update daily profit tracking
    g_dailyProfit += profit;
}

//+------------------------------------------------------------------+
//| Remove order from tracking system                               |
//+------------------------------------------------------------------+
void RemoveOrderFromTracking(int index)
{
    int size = ArraySize(g_orders);
    for(int i = index; i < size - 1; i++)
    {
        g_orders[i] = g_orders[i + 1];
    }
    ArrayResize(g_orders, size - 1);
    g_activeOrders--;
}

//+------------------------------------------------------------------+
//| Enhanced logging system                                          |
//+------------------------------------------------------------------+
void LogMessage(string message)
{
    datetime currentTime = TimeCurrent();
    string timeStr = TimeToString(currentTime, TIME_DATE | TIME_MINUTES | TIME_SECONDS);
    string logEntry = StringFormat("[%s] %s", timeStr, message);

    Print(logEntry);

    // Optional: Write to file for analysis
    // FileWrite(logHandle, logEntry);
}

//+------------------------------------------------------------------+
//| Log comprehensive trading status                                 |
//+------------------------------------------------------------------+
void LogTradingStatus()
{
    LogMessage("=== ENHANCED ANTI-MARTINGALE STATUS ===");
    LogMessage(StringFormat("Daily P&L: %.2f (%.2f%%)", g_dailyProfit,
              (g_dailyProfit / g_dailyStartBalance) * 100));
    LogMessage(StringFormat("Active Orders: %d/%d", g_activeOrders, MaxSimultaneousOrders));

    // Anti-Martingale specific metrics
    LogMessage(StringFormat("🎯 Consecutive Wins: %d", g_consecutiveWins));
    LogMessage(StringFormat("📉 Consecutive Losses: %d", g_consecutiveLosses));
    LogMessage(StringFormat("💰 Current Win Streak: %.2f", g_currentWinStreak));
    LogMessage(StringFormat("🏆 Max Win Streak: %.2f", g_maxWinStreak));
    LogMessage(StringFormat("📈 In Winning Streak: %s", g_isInWinningStreak ? "YES" : "NO"));

    // Calculate win rate from recent trades
    int wins = 0, total = 0;
    for(int i = 0; i < 10; i++)
    {
        if(g_recentTrades[i].closeTime > 0)
        {
            total++;
            if(g_recentTrades[i].isWin) wins++;
        }
    }

    if(total > 0)
    {
        double winRate = (double)wins / total * 100;
        LogMessage(StringFormat("📊 Recent Win Rate: %.1f%% (%d/%d)", winRate, wins, total));
    }

    LogMessage(StringFormat("Account Balance: %.2f", AccountInfoDouble(ACCOUNT_BALANCE)));
    LogMessage(StringFormat("Free Margin: %.2f", AccountInfoDouble(ACCOUNT_FREEMARGIN)));
    LogMessage(StringFormat("Trading Enabled: %s", g_tradingEnabled ? "YES" : "NO"));

    // Log individual positions with enhanced info
    for(int i = 0; i < ArraySize(g_orders); i++)
    {
        if(PositionSelectByTicket(g_orders[i].ticket))
        {
            double profit = PositionGetDouble(POSITION_PROFIT);
            string type = (g_orders[i].type == ORDER_TYPE_BUY) ? "BUY" : "SELL";
            LogMessage(StringFormat("Position %d: %s %.2f lots, P&L: %.2f, Signal: %.2f, Max P&L: %.2f/%.2f",
                      g_orders[i].ticket, type, g_orders[i].lots, profit,
                      g_orders[i].signalStrength, g_orders[i].maxProfit, g_orders[i].maxLoss));
        }
    }
    LogMessage("=== END STATUS ===");
}

//+------------------------------------------------------------------+
//| Get error description                                             |
//+------------------------------------------------------------------+
string ErrorDescription(int error_code)
{
    switch(error_code)
    {
        case 0: return "No error";
        case 4: return "Trade server is busy";
        case 6: return "No connection to trade server";
        case 8: return "Too frequent requests";
        case 129: return "Invalid price";
        case 130: return "Invalid stops";
        case 131: return "Invalid trade volume";
        case 132: return "Market is closed";
        case 133: return "Trade is disabled";
        case 134: return "Not enough money";
        case 135: return "Price changed";
        case 136: return "Off quotes";
        case 137: return "Broker is busy";
        case 138: return "Requote";
        case 139: return "Order is locked";
        case 140: return "Long positions only allowed";
        case 141: return "Too many requests";
        case 145: return "Modification denied because order too close to market";
        case 146: return "Trade context is busy";
        case 147: return "Expirations are denied by broker";
        case 148: return "Amount of open and pending orders has reached the limit";
        default: return StringFormat("Unknown error %d", error_code);
    }
}

//+------------------------------------------------------------------+
//| End of Enhanced Martingale EA                                    |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//|                                                    cocapepsi.mq5 |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link "https://www.mql5.com"
#property version "1.00"

#include <Trade\Trade.mqh>

//--- input parameters
input bool autoCheckTrend = false;
input bool autoCheckTrendWhenLoss = false;
input bool isSkipBonus = false;
input string type = "BTC";
input string manualStartTrend = "BUY";
input double initAccountBalance = 4000;
input double takeProfit = 0.005;
input double targetDay = 400;
input double initialLot = 0.05;

string g_trend = autoCheckTrend ? "NONE" : "";
string g_orderType = "NONE";

bool g_isBuyTrend = manualStartTrend == "BUY";
bool g_isFirstTrade = true;
bool g_isFinishing = false;

double g_priceArray[];
int g_arraySize = 0;

double g_targetProfitArray[];
int g_arraySizeTargetProfit = 0;

double g_targetDay = targetDay;
double g_collectTargetDay = 0;

double g_targetProfit = 0;
double g_accountBalance = 0;

datetime g_lastPrintTime = 0;
datetime g_bonusPrintTime = 0;

ulong currentTicketId = 0;
int count = 1;

bool startBonus = false;
bool startBonusDone = false;
bool skipBonus = isSkipBonus;
ulong currentBonusTicketId = 0;
datetime timeBonus = 0;

double chenhlech = 0;

int adxHandle = INVALID_HANDLE;

CTrade Trade;

struct TrendSignal
{
  string signal;      // "BUY" hoặc "SELL"
  int    weightPoint; // độ mạnh của tín hiệu
};

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
  g_accountBalance = initAccountBalance == 0 ? AccountInfoDouble(ACCOUNT_BALANCE) : initAccountBalance;
  g_targetProfit = g_accountBalance * takeProfit;

  Print("******************************START_INIT******************************");
  Print("TYPE: ", type);
  Print("Target day: ", g_targetDay);
  Print("Độ lớn của lệnh đầu tiên: ", initialLot);
  Print("Vốn hiện có: ", initAccountBalance);
  Print("Lợi nhuận để đóng tất cả lệnh: ", g_targetProfit);
  Print("******************************END_INIT******************************");

  Trade.SetDeviationInPoints(10);
  Trade.SetTypeFilling(ORDER_FILLING_FOK);
  Trade.SetAsyncMode(false);

  return (INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{

  if (g_collectTargetDay >= g_targetDay)
  {
    if (!g_isFinishing)
    {
      g_isFinishing = true;
      Print("***************DONE_TARGET_DAY***************");
      Print("Current Target Day: ", g_collectTargetDay);
      printPairs();
      Print("***************DONE_TARGET_DAY***************");
    }
    return;
  }

  if (g_trend == "NONE")
  {
    TrendSignal trend = checkTrend();
    Print("***************CURRENT TREND***************: ", trend.signal,
          " weight: ", trend.weightPoint);
    g_isBuyTrend = trend.signal == "BUY";
    g_trend = g_isBuyTrend ? "UP" : "DOWN";
    return;
  }

  if (g_isFirstTrade)
  {
    OpenFirstOrder();
    return;
  }

  checkAndOpenAdditionalOrders();
}

void OnDeinit(const int reason)
{
  if (adxHandle != INVALID_HANDLE)
  {
    IndicatorRelease(adxHandle);
  }
}

//+------------------------------------------------------------------+
//| Mở lệnh đầu tiên                                                 |
//+------------------------------------------------------------------+
void OpenFirstOrder()
{
  double currentPrice = SymbolInfoDouble(Symbol(), g_isBuyTrend ? SYMBOL_ASK : SYMBOL_BID);
  g_orderType = g_isBuyTrend ? "BUY" : "SELL";

  double newLots = initialLot * count;
  g_isBuyTrend
      ? Trade.Buy(newLots, Symbol(), currentPrice, 0, 0, "First BUY Order")
      : Trade.Sell(newLots, Symbol(), currentPrice, 0, 0, "First SELL Order");
  currentTicketId = Trade.ResultOrder();

  if (currentTicketId > 0)
  {
    addKeyValuePair(currentTicketId, initialLot, g_targetProfit, g_orderType, 0.0);
    g_isFirstTrade = false;
  }
}

//+------------------------------------------------------------------+
//| Kiểm tra và mở thêm lệnh                                         |
//+------------------------------------------------------------------+
void checkAndOpenAdditionalOrders()
{
  if (currentTicketId > 0 && PositionSelectByTicket(currentTicketId))
  {
    double profit = PositionGetDouble(POSITION_PROFIT);
    double targetProfit = g_targetProfit * count;

    // *********************** Bonus ***********************
    if (startBonus)
    {
      startBonusDone = true;

      datetime currentTime = TimeCurrent();
      if (currentTime - timeBonus >= 100)
      {
        startBonus = false;
      }

      double targetBonusProfit = targetProfit * 2;

      if (currentTime - g_bonusPrintTime >= 20)
      {
        Print("***************START_BONUS***************");
        Print("Profit của vị thế: ", profit);
        Print("Target Profit: ", targetBonusProfit);
        Print("Dương: " + (profit >= targetBonusProfit ? "true" : "false"));
        Print("***************END_BONUS***************");
        g_bonusPrintTime = currentTime;
      }

      if (profit >= targetBonusProfit)
      {
        Trade.PositionClose(currentTicketId);

        g_collectTargetDay += profit;
        updateValuePair(currentTicketId, profit);
        count = 1;
        currentTicketId = 0;
        g_isFirstTrade = true;
        g_trend = "NONE";
        g_orderType = "NONE";
        startBonusDone = false;
      }

      return;
    }
    // *********************** End bonus ***********************

    // *********************** Print profit ***********************
    datetime currentTime = TimeCurrent();
    if (currentTime - g_lastPrintTime >= 20)
    {
      Print("***************START_DONE***************");
      Print("Profit của vị thế: ", profit);
      Print("Target Profit: ", targetProfit);
      Print("Dương: " + (profit >= targetProfit ? "true" : "false") + " || Âm: " + (profit <= -targetProfit ? "true" : "false"));
      Print("Target Day: ", g_targetDay);
      Print("Current Target Day: ", g_collectTargetDay);
      printPairs();
      Print("***************END_DONE***************");
      g_lastPrintTime = currentTime;
    }
    // *********************** End print profit ***********************

    if (profit >= (targetProfit + chenhlech))
    {
      // *********************** Bonus ***********************
      if (!skipBonus && !startBonus && !startBonusDone)
      {
        startBonus = true;
        timeBonus = TimeCurrent();
        return;
      }
      // *********************** End bonus ***********************

      Trade.PositionClose(currentTicketId);

      g_collectTargetDay += profit;
      updateValuePair(currentTicketId, profit);
      count = 1;
      chenhlech = 0;
      currentTicketId = 0;
      g_isFirstTrade = true;
      g_trend = "NONE";
      g_orderType = "NONE";
      startBonusDone = false;
      return;
    }

    if (profit <= -targetProfit)
    {
      g_collectTargetDay += profit;
      chenhlech += (profit * -1) - targetProfit;
      Trade.PositionClose(currentTicketId);
      updateValuePair(currentTicketId, profit);
      count = count * 2;

      double nextLot = initialLot * count;
      double nextTargetProfit = g_targetProfit * count + chenhlech;

      if (autoCheckTrendWhenLoss)
      {
        TrendSignal trend = checkTrend();
        if (trend.signal == "BUY")
        {
          Print("***************When loss, start trade buy***************");
          Trade.Buy(nextLot, Symbol(), SymbolInfoDouble(Symbol(), SYMBOL_ASK), 0, 0, "First BUY Order");
          currentTicketId = Trade.ResultOrder();
          addKeyValuePair(currentTicketId, nextLot, nextTargetProfit, "BUY", 0.0);
          Print("***************When loss, start trade buy***************");
          return;
        }
        else
        {
          Print("***************When loss, start trade sell***************");
          Trade.Sell(nextLot, Symbol(), SymbolInfoDouble(Symbol(), SYMBOL_BID), 0, 0, "First SELL Order");
          currentTicketId = Trade.ResultOrder();
          addKeyValuePair(currentTicketId, nextLot, nextTargetProfit, "SELL", 0.0);
          Print("***************When loss, start trade sell***************");
          return;
        }
      }

      if (g_orderType == "BUY")
      {
        Trade.Sell(nextLot, Symbol(), SymbolInfoDouble(Symbol(), SYMBOL_BID), 0, 0, "First SELL Order");
        currentTicketId = Trade.ResultOrder();
        g_orderType = "SELL";
        addKeyValuePair(currentTicketId, nextLot, nextTargetProfit, g_orderType, 0.0);
      }
      else if (g_orderType == "SELL")
      {
        Trade.Buy(nextLot, Symbol(), SymbolInfoDouble(Symbol(), SYMBOL_ASK), 0, 0, "First BUY Order");
        currentTicketId = Trade.ResultOrder();
        g_orderType = "BUY";
        addKeyValuePair(currentTicketId, nextLot, nextTargetProfit, g_orderType, 0.0);
      }
    }
  }
}

//+------------------------------------------------------------------+
//| Array resize                                                      |
//+------------------------------------------------------------------+
void updateCurrentPriceToQueue(double currentPrice)
{
  ArrayResize(g_priceArray, g_arraySize + 1);
  g_priceArray[g_arraySize] = currentPrice;
  g_arraySize++;
}

void updateTargetProfitQueue(double profit)
{
  ArrayResize(g_targetProfitArray, g_arraySizeTargetProfit + 1);
  g_targetProfitArray[g_arraySizeTargetProfit] = profit;
  g_arraySizeTargetProfit++;
}

void resetAllQueue()
{
  ArrayResize(g_priceArray, 0);
  g_arraySize = 0;
}

double calculateAveragePrice()
{
  if (g_arraySize == 0)
    return 0;

  double sum = 0;
  for (int i = 0; i < g_arraySize; i++)
  {
    sum += g_priceArray[i];
  }

  return sum / g_arraySize;
}

//+------------------------------------------------------------------+
//| Check trend                                                       |
//+------------------------------------------------------------------+
TrendSignal checkTrend()
{
  TrendSignal result;
  int buyPoint = 0;
  int sellPoint = 0;

  bool maUp = isUptrend();
  bool maDown = isDowntrend();

  if (maUp)
    buyPoint++;
  if (maDown)
    sellPoint++;

  double adx[], plusDI[], minusDI[];
  if (getADXValues(adx, plusDI, minusDI))
  {
    if (adx[0] > 20)
    {
      if (plusDI[0] > minusDI[0])
        buyPoint++;
      else
        sellPoint++;
    }
  }

  double rsi[];
  ArraySetAsSeries(rsi, true);
  int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
  CopyBuffer(rsiHandle, 0, 0, 1, rsi);

  if (rsi[0] > 55)
    buyPoint++;
  else if (rsi[0] < 45)
    sellPoint++;

  if (isUptrendHHLL())
    buyPoint++;
  else if (isDowntrendHHLL())
    sellPoint++;

  if (isSideway())
  {
    buyPoint /= 2;
    sellPoint /= 2;
  }

  if (buyPoint >= sellPoint)
  {
    result.signal = "BUY";
    result.weightPoint = buyPoint;
  }
  else
  {
    result.signal = "SELL";
    result.weightPoint = sellPoint;
  }

  return result;
}

int getSidewayDirection()
{
  // Kiểm tra RSI
  double rsi[];
  ArraySetAsSeries(rsi, true);
  int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
  CopyBuffer(rsiHandle, 0, 0, 1, rsi);

  // Kiểm tra Bollinger Bands
  double bbUpper[], bbMiddle[], bbLower[];
  ArraySetAsSeries(bbUpper, true);
  ArraySetAsSeries(bbMiddle, true);
  ArraySetAsSeries(bbLower, true);

  int bbHandle = iBands(Symbol(), PERIOD_CURRENT, 20, 0, 2, PRICE_CLOSE);
  if (bbHandle == INVALID_HANDLE)
    return 0;

  if (CopyBuffer(bbHandle, 0, 0, 1, bbMiddle) <= 0 ||
      CopyBuffer(bbHandle, 1, 0, 1, bbUpper) <= 0 ||
      CopyBuffer(bbHandle, 2, 0, 1, bbLower) <= 0)
    return 0;

  // Lấy giá hiện tại
  double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);

  // Kiểm tra vị trí giá so với BB
  bool priceAboveMiddle = currentPrice > bbMiddle[0];
  bool priceNearUpper = currentPrice > (bbUpper[0] - (bbUpper[0] - bbMiddle[0]) * 0.3);
  bool priceNearLower = currentPrice < (bbLower[0] + (bbMiddle[0] - bbLower[0]) * 0.3);

  // Kiểm tra RSI
  bool rsiAbove50 = rsi[0] > 50;
  bool rsiBelow50 = rsi[0] < 50;

  // Đếm số điều kiện tăng
  int uptrendCount = 0;
  if (priceAboveMiddle)
    uptrendCount++;
  if (priceNearUpper)
    uptrendCount++;
  if (rsiAbove50)
    uptrendCount++;

  // Đếm số điều kiện giảm
  int downtrendCount = 0;
  if (!priceAboveMiddle)
    downtrendCount++;
  if (priceNearLower)
    downtrendCount++;
  if (rsiBelow50)
    downtrendCount++;

  // Trả về kết quả
  if (uptrendCount >= 2)
    return 1; // Xu hướng tăng trong sideway
  if (downtrendCount >= 2)
    return -1; // Xu hướng giảm trong sideway
  return 0;    // Không có xu hướng rõ ràng
}

bool isSideway()
{
  // Kiểm tra ADX
  double adx[], plusDI[], minusDI[];
  if (!getADXValues(adx, plusDI, minusDI))
    return false;

  // Kiểm tra RSI
  double rsi[];
  ArraySetAsSeries(rsi, true);
  int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
  CopyBuffer(rsiHandle, 0, 0, 1, rsi);

  // Kiểm tra Bollinger Bands
  double bbUpper[], bbMiddle[], bbLower[];
  ArraySetAsSeries(bbUpper, true);
  ArraySetAsSeries(bbMiddle, true);
  ArraySetAsSeries(bbLower, true);

  int bbHandle = iBands(Symbol(), PERIOD_CURRENT, 20, 0, 2, PRICE_CLOSE);
  if (bbHandle == INVALID_HANDLE)
    return false;

  if (CopyBuffer(bbHandle, 0, 0, 1, bbMiddle) <= 0 ||
      CopyBuffer(bbHandle, 1, 0, 1, bbUpper) <= 0 ||
      CopyBuffer(bbHandle, 2, 0, 1, bbLower) <= 0)
    return false;

  // Điều kiện sideway:
  // 1. ADX < 20 (thị trường yếu)
  // 2. RSI gần 50 (không có xu hướng rõ ràng)
  // 3. Khoảng cách BB hẹp (biến động thấp)
  bool adxCondition = adx[0] < 20;
  bool rsiCondition = rsi[0] > 45 && rsi[0] < 55;
  bool bbCondition = (bbUpper[0] - bbLower[0]) / bbMiddle[0] < 0.02; // 2% biến động

  return adxCondition && rsiCondition && bbCondition;
}

bool getMAValues(int period, double &ma[])
{
  ArraySetAsSeries(ma, true);

  int maHandle = iMA(Symbol(), PERIOD_CURRENT, period, 0, MODE_SMA, PRICE_CLOSE);

  if (maHandle == INVALID_HANDLE)
  {
    Print("Lỗi tạo MA handle");
    return false;
  }

  if (CopyBuffer(maHandle, 0, 0, 1, ma) <= 0)
  {
    Print("Lỗi copy MA buffer");
    return false;
  }

  return true;
}

bool isUptrend()
{
  double ma20[], ma50[];
  if (!getMAValues(20, ma20) || !getMAValues(50, ma50))
    return false;

  return ma20[0] > ma50[0];
}

bool isDowntrend()
{
  double ma20[], ma50[];
  if (!getMAValues(20, ma20) || !getMAValues(50, ma50))
    return false;

  return ma20[0] < ma50[0];
}

bool isStrongTrend()
{
  double adx[], plusDI[], minusDI[];
  if (!getADXValues(adx, plusDI, minusDI))
    return false;

  return adx[0] > 25;
}

bool isUptrendADX()
{
  double adx[], plusDI[], minusDI[];
  if (!getADXValues(adx, plusDI, minusDI))
    return false;

  return plusDI[0] > minusDI[0];
}

bool isUptrendHHLL()
{
  double high1 = iHigh(Symbol(), PERIOD_CURRENT, 1);
  double high2 = iHigh(Symbol(), PERIOD_CURRENT, 2);
  double low1 = iLow(Symbol(), PERIOD_CURRENT, 1);
  double low2 = iLow(Symbol(), PERIOD_CURRENT, 2);

  return (high1 > high2) && (low1 > low2);
}

bool isDowntrendHHLL()
{
  double high1 = iHigh(Symbol(), PERIOD_CURRENT, 1);
  double high2 = iHigh(Symbol(), PERIOD_CURRENT, 2);
  double low1 = iLow(Symbol(), PERIOD_CURRENT, 1);
  double low2 = iLow(Symbol(), PERIOD_CURRENT, 2);

  return (high1 < high2) && (low1 < low2);
}

bool isUptrendRSI()
{
  double rsi[];
  ArraySetAsSeries(rsi, true);
  int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
  CopyBuffer(rsiHandle, 0, 0, 1, rsi);

  return rsi[0] > 50; // RSI > 50 thường là trend tăng
}

bool isDowntrendRSI()
{
  double rsi[];
  ArraySetAsSeries(rsi, true);
  int rsiHandle = iRSI(Symbol(), PERIOD_CURRENT, 14, PRICE_CLOSE);
  CopyBuffer(rsiHandle, 0, 0, 1, rsi);

  return rsi[0] < 50; // RSI < 50 thường là trend giảm
}

bool getADXValues(double &adx[], double &plusDI[], double &minusDI[])
{
  ArraySetAsSeries(adx, true);
  ArraySetAsSeries(plusDI, true);
  ArraySetAsSeries(minusDI, true);

  int adxHandle = iADX(Symbol(), PERIOD_CURRENT, 14);

  if (adxHandle == INVALID_HANDLE)
  {
    Print("Lỗi tạo ADX handle");
    return false;
  }

  if (CopyBuffer(adxHandle, 0, 0, 1, adx) <= 0 ||
      CopyBuffer(adxHandle, 1, 0, 1, plusDI) <= 0 ||
      CopyBuffer(adxHandle, 2, 0, 1, minusDI) <= 0)
  {
    Print("Lỗi copy ADX buffers");
    return false;
  }

  return true;
}

//+------------------------------------------------------------------+
//| Object                                                            |
//+------------------------------------------------------------------+
struct KeyValuePair
{
  ulong ticketId;
  string orderType;
  double lot;
  double targetProfit;
  double receiveProfit;
};

KeyValuePair pairs[] = {};

KeyValuePair getPairValueByTicketIt(ulong ticketId)
{
  for (int i = 0; i < ArraySize(pairs); i++)
  {
    if (pairs[i].ticketId == ticketId)
      return pairs[i];
  }

  KeyValuePair defaultPair;
  defaultPair.ticketId = 0;
  defaultPair.lot = 0.0;
  defaultPair.orderType = "NONE";
  defaultPair.targetProfit = 0.0;
  defaultPair.receiveProfit = 0.0;
  return defaultPair;
}

void addKeyValuePair(ulong ticketId, double lot, double targetProfit, string orderType, double receiveProfit)
{
  int size = ArraySize(pairs);
  ArrayResize(pairs, size + 1);
  pairs[size].lot = lot;
  pairs[size].ticketId = ticketId;
  pairs[size].targetProfit = targetProfit;
  pairs[size].orderType = orderType;
  pairs[size].receiveProfit = receiveProfit;
}

void updateValuePair(ulong ticketId, double receiveProfit)
{
  for (int i = 0; i < ArraySize(pairs); i++)
  {
    if (pairs[i].ticketId == ticketId)
    {
      pairs[i].receiveProfit = receiveProfit;
      return;
    }
  }
}

void removeKeyValuePair(ulong ticketId)
{
  int size = ArraySize(pairs);
  for (int i = 0; i < size; i++)
  {
    if (pairs[i].ticketId == ticketId)
    {
      for (int j = i; j < size - 1; j++)
      {
        pairs[j] = pairs[j + 1];
      }
      ArrayResize(pairs, size - 1);
      return;
    }
  }
}

void resetPairs()
{
  ArrayResize(pairs, 0);
}

void printPairs()
{
  string output = "\n";
  for (int i = 0; i < ArraySize(pairs); i++)
  {
    output += pairs[i].ticketId + " orderType: " + pairs[i].orderType + " lot: " + DoubleToString(pairs[i].lot, 2) + " targetProfit: " + DoubleToString(pairs[i].targetProfit, 2) + " receiveProfit: " + DoubleToString(pairs[i].receiveProfit, 2);
    if (i < ArraySize(pairs) - 1)
      output += "\n";
  }
  Print("targetChild: ", output);
}
//+------------------------------------------------------------------+
//| End object                                                        |
//+------------------------------------------------------------------+

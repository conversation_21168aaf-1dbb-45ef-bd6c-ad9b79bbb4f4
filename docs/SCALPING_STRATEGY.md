# M1 Scalping Martingale EA

This document describes the updated strategy implemented in `martingale_bot_by_trend.mq5`.

## Overview
The previous version doubled the lot size endlessly after each losing trade. The new version introduces a safer approach aimed at quick M1 profits on BTC and XAU while capping risk.

### Key Features
- **EMA Cross Entry**: Trades are opened when a 5‑period EMA crosses a 20‑period EMA on the M1 chart and the RSI confirms momentum.
- **Adaptive Take‑Profit**: Profit targets are 0.1% for BTC and 0.05% for XAU. Stop loss is based on ATR to adapt to volatility.
- **Limited Martingale**: Lot size increases by a factor (default 1.5) after a loss for a maximum of two steps, preventing unlimited escalation.
- **Daily Loss Protection**: Trading stops once the accumulated loss exceeds 4% of account balance.
- **Trailing Stop**: Positions move the stop loss to break even once price moves in favour of the trade.
- **Session Awareness**: XAU trading pauses during illiquid night hours (22:00‑01:00 server time).

## Comparison to Old Logic
- Old EA relied on a complex trend‑weight system and unlimited lot doubling. The new implementation is shorter and focused on fast scalping signals.
- Risk management is stricter with defined stop losses and a daily loss limit.
- Symbol specific take‑profit levels allow separate tuning for BTC and XAU.

## Usage
Compile `martingale_bot_by_trend.mq5` in MetaTrader 5 and attach it to an M1 chart of BTCUSD or XAUUSD. Adjust the input parameters if needed to suit your broker's conditions.

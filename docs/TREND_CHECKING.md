# Trend Checking Logic

This document explains the updated trend detection used in `martingale_bot_by_trend.mq5`.

## Previous Behaviour

The old implementation of `checkTrend()` returned an integer:

- `1` – uptrend
- `-1` – downtrend
- `0` – no clear trend

The function combined MA, ADX, RSI checks and also attempted to detect sideway
conditions. No information about confidence of the signal was provided.

## New Behaviour

The revised code introduces a new structure `TrendSignal`:

```mql5
struct TrendSignal
{
  string signal;      // "BUY" or "SELL"
  int    weightPoint; // confidence score
};
```

`checkTrend()` now returns a `TrendSignal` instance. The function evaluates
several indicators (MA, ADX, RSI, HH/LL) and assigns points for BUY or SELL.
If the market is recognised as sideways these points are reduced to lower the
confidence. The final signal is whichever side has more points and the
`weightPoint` is the number of points supporting that direction.

The signal is therefore always either `BUY` or `SELL`, accompanied by a simple
weight to gauge strength.

## Usage Changes

- `OnTick()` and the loss-recovery logic now use the new return type to set the
  trading direction.
- Weight information is printed for debugging to observe the confidence level.

## Notes

This approach allows further tuning: more indicators can be incorporated or
weighting can be adjusted without altering other parts of the EA.
